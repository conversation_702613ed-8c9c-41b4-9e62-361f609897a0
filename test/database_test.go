package test

import (
	"os"
	"testing"
	"time"

	"filesystem/internal/model"
	"filesystem/internal/service"
	"filesystem/pkg/database"
)

func setupTestDB(t *testing.T) {
	// 创建临时数据库文件
	tempDB := "test_filesystem.db"
	
	// 清理可能存在的测试数据库
	os.Remove(tempDB)
	
	// 初始化测试数据库
	config := database.Config{
		DSN:             tempDB,
		MaxIdleConns:    5,
		MaxOpenConns:    10,
		ConnMaxLifetime: time.Minute,
		LogLevel:        "silent",
	}
	
	if err := database.Init(config); err != nil {
		t.Fatalf("初始化测试数据库失败: %v", err)
	}
	
	// 自动迁移表
	if err := database.AutoMigrate(&model.FileRecord{}); err != nil {
		t.Fatalf("数据库表迁移失败: %v", err)
	}
	
	// 清理函数
	t.Cleanup(func() {
		database.Close()
		os.Remove(tempDB)
	})
}

func TestFileRecordService_Create(t *testing.T) {
	setupTestDB(t)
	
	service := service.NewFileRecordService()
	
	record := &model.CreateFileRecord{
		FileKey:     "test/example.jpg",
		FileName:    "example.jpg",
		FileSize:    1024,
		ContentType: "image/jpeg",
		ETag:        "test-etag",
		FileURL:     "https://example.com/test/example.jpg",
		UploadPath:  "test",
		Operation:   model.OperationUpload,
		Status:      model.StatusSuccess,
		ClientIP:    "***********",
		UserAgent:   "test-agent",
	}
	
	result, err := service.Create(record)
	if err != nil {
		t.Fatalf("创建文件记录失败: %v", err)
	}
	
	if result.ID == 0 {
		t.Error("文件记录ID应该大于0")
	}
	
	if result.FileKey != record.FileKey {
		t.Errorf("期望文件键为 %s，实际为 %s", record.FileKey, result.FileKey)
	}
	
	if result.Operation != record.Operation {
		t.Errorf("期望操作类型为 %s，实际为 %s", record.Operation, result.Operation)
	}
}

func TestFileRecordService_GetByFileKey(t *testing.T) {
	setupTestDB(t)
	
	service := service.NewFileRecordService()
	
	// 创建测试记录
	record := &model.CreateFileRecord{
		FileKey:     "test/get-example.jpg",
		FileName:    "get-example.jpg",
		FileSize:    2048,
		ContentType: "image/jpeg",
		Operation:   model.OperationUpload,
		Status:      model.StatusSuccess,
		ClientIP:    "***********",
		UserAgent:   "test-agent",
	}
	
	created, err := service.Create(record)
	if err != nil {
		t.Fatalf("创建文件记录失败: %v", err)
	}
	
	// 根据文件键获取记录
	found, err := service.GetByFileKey(record.FileKey)
	if err != nil {
		t.Fatalf("根据文件键获取记录失败: %v", err)
	}
	
	if found.ID != created.ID {
		t.Errorf("期望记录ID为 %d，实际为 %d", created.ID, found.ID)
	}
	
	if found.FileKey != record.FileKey {
		t.Errorf("期望文件键为 %s，实际为 %s", record.FileKey, found.FileKey)
	}
}

func TestFileRecordService_List(t *testing.T) {
	setupTestDB(t)
	
	service := service.NewFileRecordService()
	
	// 创建多个测试记录
	records := []*model.CreateFileRecord{
		{
			FileKey:   "test/list1.jpg",
			FileName:  "list1.jpg",
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
			ClientIP:  "***********",
		},
		{
			FileKey:   "test/list2.jpg",
			FileName:  "list2.jpg",
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
			ClientIP:  "***********",
		},
		{
			FileKey:   "test/list3.jpg",
			FileName:  "list3.jpg",
			Operation: model.OperationDelete,
			Status:    model.StatusSuccess,
			ClientIP:  "***********",
		},
	}
	
	for _, record := range records {
		if _, err := service.Create(record); err != nil {
			t.Fatalf("创建文件记录失败: %v", err)
		}
	}
	
	// 测试列表查询
	query := &model.FileRecordQuery{
		Page:     1,
		PageSize: 10,
	}
	
	result, err := service.List(query)
	if err != nil {
		t.Fatalf("获取文件记录列表失败: %v", err)
	}
	
	if result.Total != 3 {
		t.Errorf("期望总记录数为 3，实际为 %d", result.Total)
	}
	
	if len(result.Records) != 3 {
		t.Errorf("期望返回记录数为 3，实际为 %d", len(result.Records))
	}
	
	// 测试按操作类型过滤
	query.Operation = model.OperationUpload
	result, err = service.List(query)
	if err != nil {
		t.Fatalf("按操作类型过滤失败: %v", err)
	}
	
	if result.Total != 2 {
		t.Errorf("期望上传操作记录数为 2，实际为 %d", result.Total)
	}
	
	// 测试按客户端IP过滤
	query.Operation = ""
	query.ClientIP = "***********"
	result, err = service.List(query)
	if err != nil {
		t.Fatalf("按客户端IP过滤失败: %v", err)
	}
	
	if result.Total != 2 {
		t.Errorf("期望IP为***********的记录数为 2，实际为 %d", result.Total)
	}
}

func TestFileRecordService_GetStats(t *testing.T) {
	setupTestDB(t)
	
	service := service.NewFileRecordService()
	
	// 创建测试数据
	records := []*model.CreateFileRecord{
		{
			FileKey:   "test/stats1.jpg",
			FileName:  "stats1.jpg",
			FileSize:  1024,
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
		},
		{
			FileKey:   "test/stats2.jpg",
			FileName:  "stats2.jpg",
			FileSize:  2048,
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
		},
		{
			FileKey:   "test/stats3.jpg",
			FileName:  "stats3.jpg",
			Operation: model.OperationDelete,
			Status:    model.StatusSuccess,
		},
		{
			FileKey:   "test/stats4.jpg",
			FileName:  "stats4.jpg",
			Operation: model.OperationUpload,
			Status:    model.StatusFailed,
		},
	}
	
	for _, record := range records {
		if _, err := service.Create(record); err != nil {
			t.Fatalf("创建文件记录失败: %v", err)
		}
	}
	
	// 获取统计信息
	stats, err := service.GetStats()
	if err != nil {
		t.Fatalf("获取统计信息失败: %v", err)
	}
	
	if stats.TotalFiles != 2 {
		t.Errorf("期望总文件数为 2，实际为 %d", stats.TotalFiles)
	}
	
	if stats.TotalSize != 3072 {
		t.Errorf("期望总大小为 3072，实际为 %d", stats.TotalSize)
	}
	
	if stats.UploadCount != 3 {
		t.Errorf("期望上传次数为 3，实际为 %d", stats.UploadCount)
	}
	
	if stats.DeleteCount != 1 {
		t.Errorf("期望删除次数为 1，实际为 %d", stats.DeleteCount)
	}
	
	if stats.SuccessCount != 3 {
		t.Errorf("期望成功次数为 3，实际为 %d", stats.SuccessCount)
	}
	
	if stats.FailedCount != 1 {
		t.Errorf("期望失败次数为 1，实际为 %d", stats.FailedCount)
	}
}

func TestFileRecordService_Search(t *testing.T) {
	setupTestDB(t)
	
	service := service.NewFileRecordService()
	
	// 创建测试数据
	records := []*model.CreateFileRecord{
		{
			FileKey:   "test/search-example.jpg",
			FileName:  "search-example.jpg",
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
		},
		{
			FileKey:   "test/another-file.png",
			FileName:  "another-file.png",
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
		},
		{
			FileKey:   "docs/example-doc.pdf",
			FileName:  "example-doc.pdf",
			Operation: model.OperationUpload,
			Status:    model.StatusSuccess,
		},
	}
	
	for _, record := range records {
		if _, err := service.Create(record); err != nil {
			t.Fatalf("创建文件记录失败: %v", err)
		}
	}
	
	// 搜索包含 "example" 的记录
	results, err := service.Search("example", 10)
	if err != nil {
		t.Fatalf("搜索文件记录失败: %v", err)
	}
	
	if len(results) != 2 {
		t.Errorf("期望搜索结果数为 2，实际为 %d", len(results))
	}
	
	// 验证搜索结果包含关键词
	for _, result := range results {
		if !contains(result.FileName, "example") && !contains(result.FileKey, "example") {
			t.Errorf("搜索结果应包含关键词 'example': %s", result.FileName)
		}
	}
}

// 辅助函数：检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
			 s[len(s)-len(substr):] == substr || 
			 containsInMiddle(s, substr))))
}

func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
