package test

import (
	"os"
	"testing"

	"filesystem/config"
)

func TestLoadConfig(t *testing.T) {
	// 创建临时配置文件
	tempConfig := `
server:
  port: "9090"
  mode: "test"

cos:
  secret_id: "test-secret-id"
  secret_key: "test-secret-key"
  region: "ap-beijing"
  bucket: "test-bucket"
  base_url: "https://test-bucket.cos.ap-beijing.myqcloud.com"

log:
  level: "debug"
  format: "text"
  output: "stdout"
`

	// 写入临时文件
	tempFile, err := os.CreateTemp("", "config_test_*.yaml")
	if err != nil {
		t.Fatalf("创建临时配置文件失败: %v", err)
	}
	defer os.Remove(tempFile.Name())

	if _, err := tempFile.WriteString(tempConfig); err != nil {
		t.Fatalf("写入临时配置文件失败: %v", err)
	}
	tempFile.Close()

	// 测试加载配置
	cfg, err := config.Load(tempFile.Name())
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证配置
	if cfg.Server.Port != "9090" {
		t.Errorf("期望端口为 9090，实际为 %s", cfg.Server.Port)
	}

	if cfg.Server.Mode != "test" {
		t.Errorf("期望模式为 test，实际为 %s", cfg.Server.Mode)
	}

	if cfg.COS.SecretID != "test-secret-id" {
		t.Errorf("期望 SecretID 为 test-secret-id，实际为 %s", cfg.COS.SecretID)
	}

	if cfg.Log.Level != "debug" {
		t.Errorf("期望日志级别为 debug，实际为 %s", cfg.Log.Level)
	}
}

func TestConfigFromEnv(t *testing.T) {
	// 设置环境变量
	os.Setenv("SERVER_PORT", "8888")
	os.Setenv("COS_SECRET_ID", "env-secret-id")
	defer func() {
		os.Unsetenv("SERVER_PORT")
		os.Unsetenv("COS_SECRET_ID")
	}()

	// 创建基础配置文件
	tempConfig := `
server:
  port: "8080"
  mode: "debug"

cos:
  secret_id: "file-secret-id"
  secret_key: "test-secret-key"
  region: "ap-beijing"
  bucket: "test-bucket"
  base_url: "https://test-bucket.cos.ap-beijing.myqcloud.com"

log:
  level: "info"
  format: "json"
  output: "stdout"
`

	tempFile, err := os.CreateTemp("", "config_env_test_*.yaml")
	if err != nil {
		t.Fatalf("创建临时配置文件失败: %v", err)
	}
	defer os.Remove(tempFile.Name())

	if _, err := tempFile.WriteString(tempConfig); err != nil {
		t.Fatalf("写入临时配置文件失败: %v", err)
	}
	tempFile.Close()

	// 加载配置
	cfg, err := config.Load(tempFile.Name())
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证环境变量覆盖了配置文件
	if cfg.Server.Port != "8888" {
		t.Errorf("期望端口为 8888（来自环境变量），实际为 %s", cfg.Server.Port)
	}

	if cfg.COS.SecretID != "env-secret-id" {
		t.Errorf("期望 SecretID 为 env-secret-id（来自环境变量），实际为 %s", cfg.COS.SecretID)
	}
}

func TestCreateExampleConfig(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "config_example_test_*")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建示例配置文件
	examplePath := tempDir + "/config.example.yaml"
	if err := config.CreateExampleConfig(examplePath); err != nil {
		t.Fatalf("创建示例配置文件失败: %v", err)
	}

	// 检查文件是否存在
	if _, err := os.Stat(examplePath); os.IsNotExist(err) {
		t.Errorf("示例配置文件未创建: %s", examplePath)
	}

	// 尝试加载示例配置文件
	cfg, err := config.Load(examplePath)
	if err != nil {
		t.Fatalf("加载示例配置文件失败: %v", err)
	}

	// 验证默认值
	if cfg.Server.Port != "8080" {
		t.Errorf("期望默认端口为 8080，实际为 %s", cfg.Server.Port)
	}

	if cfg.Log.Level != "info" {
		t.Errorf("期望默认日志级别为 info，实际为 %s", cfg.Log.Level)
	}
}
