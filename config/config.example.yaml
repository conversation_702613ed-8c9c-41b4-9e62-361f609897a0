# 服务器配置
server:
  port: "8080"           # 服务端口
  mode: "debug"          # 运行模式: debug, release, test

# 腾讯云 COS 配置
cos:
  secret_id: "your-secret-id"                                    # 腾讯云 SecretId
  secret_key: "your-secret-key"                                  # 腾讯云 SecretKey
  region: "ap-beijing"                                           # COS 地域
  bucket: "your-bucket-name"                                     # 存储桶名称
  base_url: "https://your-bucket-name.cos.ap-beijing.myqcloud.com" # 存储桶访问域名

# 日志配置
log:
  level: "info"          # 日志级别: debug, info, warn, error
  format: "json"         # 日志格式: json, text
  output: "stdout"       # 输出方式: stdout, file
  file: "logs/app.log"   # 日志文件路径（当 output 为 file 时使用）
