# 服务器配置
server:
  port: "8080"           # 服务端口
  mode: "debug"          # 运行模式: debug, release, test

# 腾讯云 COS 配置
cos:
  secret_id: "your-secret-id"                                    # 腾讯云 SecretId
  secret_key: "your-secret-key"                                  # 腾讯云 SecretKey
  region: "ap-beijing"                                           # COS 地域
  bucket: "your-bucket-name"                                     # 存储桶名称
  base_url: "https://your-bucket-name.cos.ap-beijing.myqcloud.com" # 存储桶访问域名

# 日志配置
log:
  level: "info"          # 日志级别: debug, info, warn, error
  format: "json"         # 日志格式: json, text
  output: "stdout"       # 输出方式: stdout, file
  file: "logs/app.log"   # 日志文件路径（当 output 为 file 时使用）

# 数据库配置
database:
  dsn: "data/filesystem.db"        # 数据库连接字符串
  max_idle_conns: 10               # 最大空闲连接数
  max_open_conns: 100              # 最大打开连接数
  conn_max_lifetime: 3600          # 连接最大生存时间（秒）
  log_level: "warn"                # 数据库日志级别: silent, error, warn, info
