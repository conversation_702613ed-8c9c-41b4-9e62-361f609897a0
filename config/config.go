package config

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 应用配置结构
type Config struct {
	Server   ServerConfig   `yaml:"server"`
	COS      COSConfig      `yaml:"cos"`
	Log      LogConfig      `yaml:"log"`
	Database DatabaseConfig `yaml:"database"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port string `yaml:"port"`
	Mode string `yaml:"mode"` // debug, release, test
}

// COSConfig 腾讯云 COS 配置
type COSConfig struct {
	SecretID  string `yaml:"secret_id"`
	SecretKey string `yaml:"secret_key"`
	Region    string `yaml:"region"`
	Bucket    string `yaml:"bucket"`
	BaseURL   string `yaml:"base_url"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level  string `yaml:"level"`  // debug, info, warn, error
	Format string `yaml:"format"` // json, text
	Output string `yaml:"output"` // stdout, file
	File   string `yaml:"file"`   // 日志文件路径
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	DSN             string `yaml:"dsn"`               // 数据库连接字符串
	MaxIdleConns    int    `yaml:"max_idle_conns"`    // 最大空闲连接数
	MaxOpenConns    int    `yaml:"max_open_conns"`    // 最大打开连接数
	ConnMaxLifetime int    `yaml:"conn_max_lifetime"` // 连接最大生存时间（秒）
	LogLevel        string `yaml:"log_level"`         // 日志级别
}

var globalConfig *Config

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	if configPath == "" {
		configPath = "config/config.yaml"
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 从环境变量覆盖配置
	overrideFromEnv(&config)

	// 设置默认值
	setDefaults(&config)

	globalConfig = &config
	return &config, nil
}

// Get 获取全局配置
func Get() *Config {
	return globalConfig
}

// overrideFromEnv 从环境变量覆盖配置
func overrideFromEnv(config *Config) {
	if port := os.Getenv("SERVER_PORT"); port != "" {
		config.Server.Port = port
	}
	if mode := os.Getenv("SERVER_MODE"); mode != "" {
		config.Server.Mode = mode
	}
	if secretID := os.Getenv("COS_SECRET_ID"); secretID != "" {
		config.COS.SecretID = secretID
	}
	if secretKey := os.Getenv("COS_SECRET_KEY"); secretKey != "" {
		config.COS.SecretKey = secretKey
	}
	if region := os.Getenv("COS_REGION"); region != "" {
		config.COS.Region = region
	}
	if bucket := os.Getenv("COS_BUCKET"); bucket != "" {
		config.COS.Bucket = bucket
	}
	if baseURL := os.Getenv("COS_BASE_URL"); baseURL != "" {
		config.COS.BaseURL = baseURL
	}
	if level := os.Getenv("LOG_LEVEL"); level != "" {
		config.Log.Level = level
	}
	if dsn := os.Getenv("DATABASE_DSN"); dsn != "" {
		config.Database.DSN = dsn
	}
	if logLevel := os.Getenv("DATABASE_LOG_LEVEL"); logLevel != "" {
		config.Database.LogLevel = logLevel
	}
}

// setDefaults 设置默认值
func setDefaults(config *Config) {
	if config.Server.Port == "" {
		config.Server.Port = "8080"
	}
	if config.Server.Mode == "" {
		config.Server.Mode = "debug"
	}
	if config.Log.Level == "" {
		config.Log.Level = "info"
	}
	if config.Log.Format == "" {
		config.Log.Format = "json"
	}
	if config.Log.Output == "" {
		config.Log.Output = "stdout"
	}
	if config.Database.DSN == "" {
		config.Database.DSN = "data/filesystem.db"
	}
	if config.Database.MaxIdleConns == 0 {
		config.Database.MaxIdleConns = 10
	}
	if config.Database.MaxOpenConns == 0 {
		config.Database.MaxOpenConns = 100
	}
	if config.Database.ConnMaxLifetime == 0 {
		config.Database.ConnMaxLifetime = 3600 // 1小时
	}
	if config.Database.LogLevel == "" {
		config.Database.LogLevel = "warn"
	}
}

// GetConfigPath 获取配置文件路径
func GetConfigPath() string {
	if path := os.Getenv("CONFIG_PATH"); path != "" {
		return path
	}
	
	// 尝试不同的配置文件位置
	paths := []string{
		"config/config.yaml",
		"config.yaml",
		"/etc/filesystem/config.yaml",
	}
	
	for _, path := range paths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}
	
	return "config/config.yaml"
}

// CreateExampleConfig 创建示例配置文件
func CreateExampleConfig(path string) error {
	exampleConfig := Config{
		Server: ServerConfig{
			Port: "8080",
			Mode: "debug",
		},
		COS: COSConfig{
			SecretID:  "your-secret-id",
			SecretKey: "your-secret-key",
			Region:    "ap-beijing",
			Bucket:    "your-bucket-name",
			BaseURL:   "https://your-bucket-name.cos.ap-beijing.myqcloud.com",
		},
		Log: LogConfig{
			Level:  "info",
			Format: "json",
			Output: "stdout",
			File:   "logs/app.log",
		},
		Database: DatabaseConfig{
			DSN:             "data/filesystem.db",
			MaxIdleConns:    10,
			MaxOpenConns:    100,
			ConnMaxLifetime: 3600,
			LogLevel:        "warn",
		},
	}

	data, err := yaml.Marshal(&exampleConfig)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	if err := os.WriteFile(path, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}
