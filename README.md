# Filesystem - 腾讯云 COS 对象存储服务

基于 Gin 框架的腾讯云 COS 对象存储 RESTful API 服务。

## 项目结构

```
filesystem/
├── cmd/                    # 应用程序入口
│   └── server/            # 服务器启动代码
├── config/                # 配置文件
├── internal/              # 内部应用代码
│   ├── handler/           # HTTP 处理器
│   ├── service/           # 业务逻辑层
│   └── model/             # 数据模型
├── pkg/                   # 可复用的包
│   ├── cos/               # 腾讯云 COS 客户端封装
│   ├── response/          # 统一响应格式
│   └── logger/            # 日志工具
├── docs/                  # 文档
├── test/                  # 测试文件
├── go.mod                 # Go 模块依赖
└── README.md              # 项目说明
```

## 功能特性

- 基于 Gin 框架的高性能 HTTP 服务
- 腾讯云 COS 对象存储集成
- RESTful API 设计
- 统一的错误处理和响应格式
- 结构化日志记录
- 配置文件管理

## 快速开始

### 环境要求

- Go 1.19+
- 腾讯云 COS 账户和密钥

### 安装依赖

```bash
go mod tidy
```

### 配置

复制配置文件并修改相关参数：

```bash
cp config/config.example.yaml config/config.yaml
```

### 运行

```bash
go run cmd/server/main.go
```

## API 接口

详细的 API 文档请查看 [docs/api.md](docs/api.md)
