# Filesystem - 腾讯云 COS 对象存储服务

基于 Gin 框架的腾讯云 COS 对象存储 RESTful API 服务。

## 项目结构

```
filesystem/
├── cmd/                    # 应用程序入口
│   └── server/            # 服务器启动代码
├── config/                # 配置文件
├── internal/              # 内部应用代码
│   ├── handler/           # HTTP 处理器
│   ├── service/           # 业务逻辑层
│   └── model/             # 数据模型
├── pkg/                   # 可复用的包
│   ├── cos/               # 腾讯云 COS 客户端封装
│   ├── response/          # 统一响应格式
│   └── logger/            # 日志工具
├── docs/                  # 文档
├── test/                  # 测试文件
├── go.mod                 # Go 模块依赖
└── README.md              # 项目说明
```

## 功能特性

- 基于 Gin 框架的高性能 HTTP 服务
- 腾讯云 COS 对象存储集成
- RESTful API 设计
- 统一的错误处理和响应格式
- 结构化日志记录
- 配置文件管理

## 快速开始

### 环境要求

- Go 1.19+
- 腾讯云 COS 账户和密钥

### 安装依赖

```bash
go mod tidy
```

### 配置

复制配置文件并修改相关参数：

```bash
cp config/config.example.yaml config/config.yaml
```

### 运行

```bash
go run cmd/server/main.go
```

## 数据库

项目使用 SQLite 数据库存储文件操作记录，包括：

- 文件上传记录
- 文件删除记录
- 文件复制记录
- 操作状态和错误信息
- 客户端信息和时间戳

数据库文件默认存储在 `data/filesystem.db`，可通过配置文件修改。

## API 接口

### 文件操作接口

- `POST /api/v1/files/upload` - 上传文件
- `GET /api/v1/files/download/{key}` - 下载文件
- `DELETE /api/v1/files/{key}` - 删除文件
- `GET /api/v1/files/` - 列出文件
- `GET /api/v1/files/info/{key}` - 获取文件信息
- `POST /api/v1/files/copy` - 复制文件
- `POST /api/v1/files/batch-delete` - 批量删除文件
- `GET /api/v1/files/exists/{key}` - 检查文件是否存在

### 文件记录接口

- `GET /api/v1/records/` - 获取文件记录列表
- `GET /api/v1/records/{id}` - 获取单个文件记录
- `GET /api/v1/records/key/{key}` - 根据文件键获取记录
- `GET /api/v1/records/stats` - 获取统计信息
- `GET /api/v1/records/search` - 搜索文件记录

详细的 API 文档请查看 [docs/api.md](docs/api.md)
