# Filesystem API 文档

基于 Gin 框架的腾讯云 COS 对象存储 RESTful API 服务。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **Content-Type**: `application/json` (除文件上传外)
- **响应格式**: JSON

## 统一响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "error": ""
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | COS 操作错误 |
| 1002 | 配置错误 |

## API 接口

### 1. 健康检查

检查服务状态。

**请求**
```
GET /api/v1/health
```

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "status": "ok",
    "timestamp": {},
    "service": "filesystem"
  }
}
```

### 2. 上传文件

上传文件到 COS 存储。

**请求**
```
POST /api/v1/files/upload
Content-Type: multipart/form-data
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file | file | 是 | 要上传的文件 |
| path | string | 否 | 上传路径 |
| content_type | string | 否 | 内容类型 |
| overwrite | bool | 否 | 是否覆盖已存在文件 |

**响应**
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "file_name": "example.jpg",
    "file_key": "2024/01/01/example.jpg",
    "file_size": 1024,
    "file_url": "https://bucket.cos.region.myqcloud.com/2024/01/01/example.jpg",
    "content_type": "image/jpeg",
    "etag": "\"d41d8cd98f00b204e9800998ecf8427e\""
  }
}
```

### 3. 下载文件

下载指定的文件。

**请求**
```
GET /api/v1/files/download/{key}
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| key | string | 是 | 文件键（路径参数） |

**响应**
- 成功：返回文件内容（二进制流）
- 失败：返回 JSON 错误信息

### 4. 删除文件

删除指定的文件。

**请求**
```
DELETE /api/v1/files/{key}
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| key | string | 是 | 文件键（路径参数） |

**响应**
```json
{
  "code": 200,
  "message": "文件删除成功",
  "data": {
    "key": "2024/01/01/example.jpg"
  }
}
```

### 5. 列出文件

列出指定前缀的文件和目录。

**请求**
```
GET /api/v1/files/
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| prefix | string | 否 | 文件前缀 |
| delimiter | string | 否 | 分隔符 |
| marker | string | 否 | 起始标记 |
| max_keys | int | 否 | 最大返回数量（默认100，最大1000） |

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "files": [
      {
        "key": "2024/01/01/example.jpg",
        "name": "example.jpg",
        "size": 1024,
        "last_modified": "2024-01-01T12:00:00Z",
        "etag": "\"d41d8cd98f00b204e9800998ecf8427e\"",
        "content_type": "image/jpeg",
        "url": "https://bucket.cos.region.myqcloud.com/2024/01/01/example.jpg"
      }
    ],
    "directories": ["2024/01/02/"],
    "prefix": "2024/01/01/",
    "marker": "",
    "is_truncated": false,
    "next_marker": ""
  }
}
```

### 6. 获取文件信息

获取指定文件的详细信息。

**请求**
```
GET /api/v1/files/info/{key}
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| key | string | 是 | 文件键（路径参数） |

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "key": "2024/01/01/example.jpg",
    "name": "example.jpg",
    "size": 1024,
    "last_modified": "2024-01-01T12:00:00Z",
    "etag": "\"d41d8cd98f00b204e9800998ecf8427e\"",
    "content_type": "image/jpeg",
    "url": "https://bucket.cos.region.myqcloud.com/2024/01/01/example.jpg",
    "exists": true
  }
}
```

### 7. 复制文件

复制文件到新位置。

**请求**
```
POST /api/v1/files/copy
Content-Type: application/json
```

**参数**
```json
{
  "source_key": "2024/01/01/example.jpg",
  "dest_key": "2024/01/02/example_copy.jpg"
}
```

**响应**
```json
{
  "code": 200,
  "message": "文件复制成功",
  "data": {
    "source_key": "2024/01/01/example.jpg",
    "dest_key": "2024/01/02/example_copy.jpg",
    "success": true
  }
}
```

### 8. 批量删除文件

批量删除多个文件。

**请求**
```
POST /api/v1/files/batch-delete
Content-Type: application/json
```

**参数**
```json
{
  "keys": [
    "2024/01/01/example1.jpg",
    "2024/01/01/example2.jpg"
  ]
}
```

**响应**
```json
{
  "code": 200,
  "message": "批量删除文件完成",
  "data": {
    "success": [
      "2024/01/01/example1.jpg"
    ],
    "failed": [
      "2024/01/01/example2.jpg"
    ]
  }
}
```

### 9. 检查文件是否存在

检查指定文件是否存在。

**请求**
```
GET /api/v1/files/exists/{key}
```

**参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| key | string | 是 | 文件键（路径参数） |

**响应**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "key": "2024/01/01/example.jpg",
    "exists": true
  }
}
```

## 错误处理

当请求失败时，API 会返回相应的错误信息：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "具体的错误详情"
}
```

## 使用示例

### 使用 curl 上传文件

```bash
curl -X POST \
  http://localhost:8080/api/v1/files/upload \
  -F "file=@/path/to/your/file.jpg" \
  -F "path=images" \
  -F "overwrite=true"
```

### 使用 curl 下载文件

```bash
curl -X GET \
  http://localhost:8080/api/v1/files/download/2024/01/01/example.jpg \
  -o downloaded_file.jpg
```

### 使用 curl 列出文件

```bash
curl -X GET \
  "http://localhost:8080/api/v1/files/?prefix=2024/01/01/&max_keys=10"
```
