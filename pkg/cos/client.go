package cos

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/tencentyun/cos-go-sdk-v5"
)

// Client COS 客户端
type Client struct {
	client  *cos.Client
	bucket  string
	baseURL string
}

// Config COS 配置
type Config struct {
	SecretID  string
	SecretKey string
	Region    string
	Bucket    string
	BaseURL   string
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string    `json:"key"`           // 文件键
	Size         int64     `json:"size"`          // 文件大小
	LastModified time.Time `json:"last_modified"` // 最后修改时间
	ETag         string    `json:"etag"`          // ETag
	ContentType  string    `json:"content_type"`  // 内容类型
	URL          string    `json:"url"`           // 访问URL
}

// UploadResult 上传结果
type UploadResult struct {
	Key         string `json:"key"`          // 文件键
	Size        int64  `json:"size"`         // 文件大小
	ETag        string `json:"etag"`         // ETag
	URL         string `json:"url"`          // 访问URL
	ContentType string `json:"content_type"` // 内容类型
}

// ListResult 列表结果
type ListResult struct {
	Files       []FileInfo `json:"files"`        // 文件列表
	Directories []string   `json:"directories"`  // 目录列表
	Prefix      string     `json:"prefix"`       // 前缀
	Marker      string     `json:"marker"`       // 标记
	IsTruncated bool       `json:"is_truncated"` // 是否截断
	NextMarker  string     `json:"next_marker"`  // 下一个标记
}

// NewClient 创建 COS 客户端
func NewClient(config Config) (*Client, error) {
	// 解析 bucket URL
	bucketURL, err := url.Parse(config.BaseURL)
	if err != nil {
		return nil, fmt.Errorf("解析 bucket URL 失败: %v", err)
	}

	// 创建 COS 客户端
	client := cos.NewClient(&cos.BaseURL{
		BucketURL: bucketURL,
	}, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.SecretID,
			SecretKey: config.SecretKey,
		},
	})

	return &Client{
		client:  client,
		bucket:  config.Bucket,
		baseURL: config.BaseURL,
	}, nil
}

// Upload 上传文件
func (c *Client) Upload(ctx context.Context, key string, reader io.Reader, contentType string) (*UploadResult, error) {
	// 如果没有指定 content type，尝试从文件扩展名推断
	if contentType == "" {
		contentType = getContentType(key)
	}

	// 上传文件
	resp, err := c.client.Object.Put(ctx, key, reader, &cos.ObjectPutOptions{
		ObjectPutHeaderOptions: &cos.ObjectPutHeaderOptions{
			ContentType: contentType,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %v", err)
	}

	// 获取文件信息
	headResp, err := c.client.Object.Head(ctx, key, nil)
	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	size := headResp.ContentLength
	if size == 0 && headResp.Header.Get("Content-Length") != "" {
		// 尝试从 header 获取大小
		if cl := headResp.Header.Get("Content-Length"); cl != "" {
			fmt.Sscanf(cl, "%d", &size)
		}
	}

	return &UploadResult{
		Key:         key,
		Size:        size,
		ETag:        resp.Header.Get("ETag"),
		URL:         c.getFileURL(key),
		ContentType: contentType,
	}, nil
}

// Download 下载文件
func (c *Client) Download(ctx context.Context, key string) (io.ReadCloser, error) {
	resp, err := c.client.Object.Get(ctx, key, nil)
	if err != nil {
		return nil, fmt.Errorf("下载文件失败: %v", err)
	}
	return resp.Body, nil
}

// Delete 删除文件
func (c *Client) Delete(ctx context.Context, key string) error {
	_, err := c.client.Object.Delete(ctx, key)
	if err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}
	return nil
}

// GetFileInfo 获取文件信息
func (c *Client) GetFileInfo(ctx context.Context, key string) (*FileInfo, error) {
	resp, err := c.client.Object.Head(ctx, key, nil)
	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	lastModified, _ := time.Parse(time.RFC1123, resp.Header.Get("Last-Modified"))

	return &FileInfo{
		Key:          key,
		Size:         resp.ContentLength,
		LastModified: lastModified,
		ETag:         resp.Header.Get("ETag"),
		ContentType:  resp.Header.Get("Content-Type"),
		URL:          c.getFileURL(key),
	}, nil
}

// List 列出文件
func (c *Client) List(ctx context.Context, prefix string, delimiter string, marker string, maxKeys int) (*ListResult, error) {
	if maxKeys <= 0 {
		maxKeys = 1000
	}

	resp, err := c.client.Bucket.Get(ctx, &cos.BucketGetOptions{
		Prefix:    prefix,
		Delimiter: delimiter,
		Marker:    marker,
		MaxKeys:   maxKeys,
	})
	if err != nil {
		return nil, fmt.Errorf("列出文件失败: %v", err)
	}

	result := &ListResult{
		Files:       make([]FileInfo, 0, len(resp.Contents)),
		Directories: make([]string, 0, len(resp.CommonPrefixes)),
		Prefix:      prefix,
		Marker:      marker,
		IsTruncated: resp.IsTruncated,
		NextMarker:  resp.NextMarker,
	}

	// 处理文件
	for _, content := range resp.Contents {
		lastModified, _ := time.Parse(time.RFC3339, content.LastModified)
		result.Files = append(result.Files, FileInfo{
			Key:          content.Key,
			Size:         content.Size,
			LastModified: lastModified,
			ETag:         content.ETag,
			URL:          c.getFileURL(content.Key),
		})
	}

	// 处理目录
	for _, prefix := range resp.CommonPrefixes {
		result.Directories = append(result.Directories, prefix)
	}

	return result, nil
}

// Exists 检查文件是否存在
func (c *Client) Exists(ctx context.Context, key string) (bool, error) {
	_, err := c.client.Object.Head(ctx, key, nil)
	if err != nil {
		if cos.IsNotFoundError(err) {
			return false, nil
		}
		return false, fmt.Errorf("检查文件是否存在失败: %v", err)
	}
	return true, nil
}

// Copy 复制文件
func (c *Client) Copy(ctx context.Context, sourceKey, destKey string) error {
	sourceURL := fmt.Sprintf("%s/%s", c.baseURL, sourceKey)
	_, err := c.client.Object.Copy(ctx, destKey, sourceURL, nil)
	if err != nil {
		return fmt.Errorf("复制文件失败: %v", err)
	}
	return nil
}

// getFileURL 获取文件访问URL
func (c *Client) getFileURL(key string) string {
	return fmt.Sprintf("%s/%s", strings.TrimRight(c.baseURL, "/"), key)
}

// getContentType 根据文件扩展名获取 Content-Type
func getContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	case ".txt":
		return "text/plain"
	case ".html":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".zip":
		return "application/zip"
	case ".mp4":
		return "video/mp4"
	case ".mp3":
		return "audio/mpeg"
	default:
		return "application/octet-stream"
	}
}
