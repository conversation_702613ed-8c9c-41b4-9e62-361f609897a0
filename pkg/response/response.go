package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`              // 状态码
	Message string      `json:"message"`           // 消息
	Data    interface{} `json:"data,omitempty"`    // 数据
	Error   string      `json:"error,omitempty"`   // 错误信息
}

// 预定义状态码
const (
	CodeSuccess      = 200  // 成功
	CodeBadRequest   = 400  // 请求参数错误
	CodeUnauthorized = 401  // 未授权
	CodeForbidden    = 403  // 禁止访问
	CodeNotFound     = 404  // 资源不存在
	CodeServerError  = 500  // 服务器内部错误
	CodeCOSError     = 1001 // COS 操作错误
	CodeConfigError  = 1002 // 配置错误
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: "success",
		Data:    data,
	})
}

// SuccessWithMessage 带消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	httpStatus := getHTTPStatus(code)
	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
	})
}

// ErrorWithDetail 带详细错误信息的错误响应
func ErrorWithDetail(c *gin.Context, code int, message string, err error) {
	httpStatus := getHTTPStatus(code)
	resp := Response{
		Code:    code,
		Message: message,
	}
	if err != nil {
		resp.Error = err.Error()
	}
	c.JSON(httpStatus, resp)
}

// BadRequest 请求参数错误
func BadRequest(c *gin.Context, message string) {
	Error(c, CodeBadRequest, message)
}

// BadRequestWithDetail 带详细信息的请求参数错误
func BadRequestWithDetail(c *gin.Context, message string, err error) {
	ErrorWithDetail(c, CodeBadRequest, message, err)
}

// Unauthorized 未授权
func Unauthorized(c *gin.Context, message string) {
	Error(c, CodeUnauthorized, message)
}

// Forbidden 禁止访问
func Forbidden(c *gin.Context, message string) {
	Error(c, CodeForbidden, message)
}

// NotFound 资源不存在
func NotFound(c *gin.Context, message string) {
	Error(c, CodeNotFound, message)
}

// ServerError 服务器内部错误
func ServerError(c *gin.Context, message string) {
	Error(c, CodeServerError, message)
}

// ServerErrorWithDetail 带详细信息的服务器内部错误
func ServerErrorWithDetail(c *gin.Context, message string, err error) {
	ErrorWithDetail(c, CodeServerError, message, err)
}

// COSError COS 操作错误
func COSError(c *gin.Context, message string, err error) {
	ErrorWithDetail(c, CodeCOSError, message, err)
}

// ConfigError 配置错误
func ConfigError(c *gin.Context, message string, err error) {
	ErrorWithDetail(c, CodeConfigError, message, err)
}

// getHTTPStatus 根据业务状态码获取 HTTP 状态码
func getHTTPStatus(code int) int {
	switch code {
	case CodeSuccess:
		return http.StatusOK
	case CodeBadRequest:
		return http.StatusBadRequest
	case CodeUnauthorized:
		return http.StatusUnauthorized
	case CodeForbidden:
		return http.StatusForbidden
	case CodeNotFound:
		return http.StatusNotFound
	case CodeServerError, CodeCOSError, CodeConfigError:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// PageData 分页数据结构
type PageData struct {
	List     interface{} `json:"list"`      // 数据列表
	Total    int64       `json:"total"`     // 总数
	Page     int         `json:"page"`      // 当前页
	PageSize int         `json:"page_size"` // 每页大小
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, list interface{}, total int64, page, pageSize int) {
	Success(c, PageData{
		List:     list,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	})
}

// FileUploadResult 文件上传结果
type FileUploadResult struct {
	FileName string `json:"file_name"` // 文件名
	FileSize int64  `json:"file_size"` // 文件大小
	FileURL  string `json:"file_url"`  // 文件访问URL
	FileKey  string `json:"file_key"`  // 文件存储键
}

// SuccessWithFile 文件操作成功响应
func SuccessWithFile(c *gin.Context, result FileUploadResult) {
	Success(c, result)
}
