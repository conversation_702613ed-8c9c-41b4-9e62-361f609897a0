package model

import "time"

// UploadRequest 上传请求
type UploadRequest struct {
	Path        string `form:"path" json:"path"`                 // 上传路径（可选）
	ContentType string `form:"content_type" json:"content_type"` // 内容类型（可选）
	Overwrite   bool   `form:"overwrite" json:"overwrite"`       // 是否覆盖（可选，默认false）
}

// UploadResponse 上传响应
type UploadResponse struct {
	FileName    string `json:"file_name"`    // 文件名
	FileKey     string `json:"file_key"`     // 文件键
	FileSize    int64  `json:"file_size"`    // 文件大小
	FileURL     string `json:"file_url"`     // 文件访问URL
	ContentType string `json:"content_type"` // 内容类型
	ETag        string `json:"etag"`         // ETag
}

// DownloadRequest 下载请求
type DownloadRequest struct {
	Key string `uri:"key" binding:"required"` // 文件键
}

// DeleteRequest 删除请求
type DeleteRequest struct {
	Key string `uri:"key" binding:"required"` // 文件键
}

// ListRequest 列表请求
type ListRequest struct {
	Prefix    string `form:"prefix"`     // 前缀
	Delimiter string `form:"delimiter"`  // 分隔符
	Marker    string `form:"marker"`     // 标记
	MaxKeys   int    `form:"max_keys"`   // 最大键数
}

// ListResponse 列表响应
type ListResponse struct {
	Files       []FileItem `json:"files"`        // 文件列表
	Directories []string   `json:"directories"`  // 目录列表
	Prefix      string     `json:"prefix"`       // 前缀
	Marker      string     `json:"marker"`       // 标记
	IsTruncated bool       `json:"is_truncated"` // 是否截断
	NextMarker  string     `json:"next_marker"`  // 下一个标记
}

// FileItem 文件项
type FileItem struct {
	Key          string    `json:"key"`           // 文件键
	Name         string    `json:"name"`          // 文件名
	Size         int64     `json:"size"`          // 文件大小
	LastModified time.Time `json:"last_modified"` // 最后修改时间
	ETag         string    `json:"etag"`          // ETag
	ContentType  string    `json:"content_type"`  // 内容类型
	URL          string    `json:"url"`           // 访问URL
}

// FileInfoRequest 文件信息请求
type FileInfoRequest struct {
	Key string `uri:"key" binding:"required"` // 文件键
}

// FileInfoResponse 文件信息响应
type FileInfoResponse struct {
	Key          string    `json:"key"`           // 文件键
	Name         string    `json:"name"`          // 文件名
	Size         int64     `json:"size"`          // 文件大小
	LastModified time.Time `json:"last_modified"` // 最后修改时间
	ETag         string    `json:"etag"`          // ETag
	ContentType  string    `json:"content_type"`  // 内容类型
	URL          string    `json:"url"`           // 访问URL
	Exists       bool      `json:"exists"`        // 是否存在
}

// CopyRequest 复制请求
type CopyRequest struct {
	SourceKey string `json:"source_key" binding:"required"` // 源文件键
	DestKey   string `json:"dest_key" binding:"required"`   // 目标文件键
}

// CopyResponse 复制响应
type CopyResponse struct {
	SourceKey string `json:"source_key"` // 源文件键
	DestKey   string `json:"dest_key"`   // 目标文件键
	Success   bool   `json:"success"`    // 是否成功
}

// BatchDeleteRequest 批量删除请求
type BatchDeleteRequest struct {
	Keys []string `json:"keys" binding:"required,min=1"` // 文件键列表
}

// BatchDeleteResponse 批量删除响应
type BatchDeleteResponse struct {
	Success []string `json:"success"` // 成功删除的文件键
	Failed  []string `json:"failed"`  // 删除失败的文件键
}

// ExistsRequest 检查存在请求
type ExistsRequest struct {
	Key string `uri:"key" binding:"required"` // 文件键
}

// ExistsResponse 检查存在响应
type ExistsResponse struct {
	Key    string `json:"key"`    // 文件键
	Exists bool   `json:"exists"` // 是否存在
}
