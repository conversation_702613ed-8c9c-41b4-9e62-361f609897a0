package model

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// FileRecord 文件记录模型
type FileRecord struct {
	ID          uint           `json:"id" gorm:"primaryKey"`                 // 主键ID
	FileKey     string         `json:"file_key" gorm:"uniqueIndex;not null"` // 文件键（唯一）
	FileName    string         `json:"file_name" gorm:"not null"`            // 原始文件名
	FileSize    int64          `json:"file_size" gorm:"not null"`            // 文件大小（字节）
	ContentType string         `json:"content_type"`                         // 内容类型
	ETag        string         `json:"etag"`                                 // ETag
	FileURL     string         `json:"file_url"`                             // 文件访问URL
	UploadPath  string         `json:"upload_path"`                          // 上传路径
	Operation   string         `json:"operation" gorm:"not null"`            // 操作类型（upload, delete, copy）
	Status      string         `json:"status" gorm:"default:'success'"`      // 状态（success, failed）
	ErrorMsg    string         `json:"error_msg"`                            // 错误信息
	ClientIP    string         `json:"client_ip"`                            // 客户端IP
	UserAgent   string         `json:"user_agent"`                           // 用户代理
	CreatedAt   time.Time      `json:"created_at"`                           // 创建时间
	UpdatedAt   time.Time      `json:"updated_at"`                           // 更新时间
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`              // 软删除时间
}

// TableName 指定表名
func (FileRecord) TableName() string {
	return "file_records"
}

// FileRecordQuery 文件记录查询参数
type FileRecordQuery struct {
	FileKey   string    `form:"file_key" json:"file_key"`     // 文件键
	FileName  string    `form:"file_name" json:"file_name"`   // 文件名（模糊查询）
	Operation string    `form:"operation" json:"operation"`   // 操作类型
	Status    string    `form:"status" json:"status"`         // 状态
	ClientIP  string    `form:"client_ip" json:"client_ip"`   // 客户端IP
	StartTime time.Time `form:"start_time" json:"start_time"` // 开始时间
	EndTime   time.Time `form:"end_time" json:"end_time"`     // 结束时间
	Page      int       `form:"page" json:"page"`             // 页码
	PageSize  int       `form:"page_size" json:"page_size"`   // 每页大小
	OrderBy   string    `form:"order_by" json:"order_by"`     // 排序字段
	OrderDir  string    `form:"order_dir" json:"order_dir"`   // 排序方向（asc, desc）
}

// FileRecordList 文件记录列表响应
type FileRecordList struct {
	Records  []FileRecord `json:"records"`   // 记录列表
	Total    int64        `json:"total"`     // 总数
	Page     int          `json:"page"`      // 当前页
	PageSize int          `json:"page_size"` // 每页大小
}

// FileRecordStats 文件记录统计
type FileRecordStats struct {
	TotalFiles   int64 `json:"total_files"`   // 总文件数
	TotalSize    int64 `json:"total_size"`    // 总大小
	UploadCount  int64 `json:"upload_count"`  // 上传次数
	DeleteCount  int64 `json:"delete_count"`  // 删除次数
	CopyCount    int64 `json:"copy_count"`    // 复制次数
	SuccessCount int64 `json:"success_count"` // 成功次数
	FailedCount  int64 `json:"failed_count"`  // 失败次数
	TodayUploads int64 `json:"today_uploads"` // 今日上传
	WeekUploads  int64 `json:"week_uploads"`  // 本周上传
	MonthUploads int64 `json:"month_uploads"` // 本月上传
}

// CreateFileRecord 创建文件记录
type CreateFileRecord struct {
	FileKey     string `json:"file_key" binding:"required"`  // 文件键
	FileName    string `json:"file_name" binding:"required"` // 原始文件名
	FileSize    int64  `json:"file_size"`                    // 文件大小
	ContentType string `json:"content_type"`                 // 内容类型
	ETag        string `json:"etag"`                         // ETag
	FileURL     string `json:"file_url"`                     // 文件访问URL
	UploadPath  string `json:"upload_path"`                  // 上传路径
	Operation   string `json:"operation" binding:"required"` // 操作类型
	Status      string `json:"status"`                       // 状态
	ErrorMsg    string `json:"error_msg"`                    // 错误信息
	ClientIP    string `json:"client_ip"`                    // 客户端IP
	UserAgent   string `json:"user_agent"`                   // 用户代理
}

// UpdateFileRecord 更新文件记录
type UpdateFileRecord struct {
	FileSize    *int64  `json:"file_size"`    // 文件大小
	ContentType *string `json:"content_type"` // 内容类型
	ETag        *string `json:"etag"`         // ETag
	FileURL     *string `json:"file_url"`     // 文件访问URL
	Status      *string `json:"status"`       // 状态
	ErrorMsg    *string `json:"error_msg"`    // 错误信息
}

// 操作类型常量
const (
	OperationUpload = "upload" // 上传
	OperationDelete = "delete" // 删除
	OperationCopy   = "copy"   // 复制
	OperationMove   = "move"   // 移动
)

// 状态常量
const (
	StatusSuccess = "success" // 成功
	StatusFailed  = "failed"  // 失败
	StatusPending = "pending" // 处理中
)

// Validate 验证创建文件记录参数
func (c *CreateFileRecord) Validate() error {
	if c.FileKey == "" {
		return fmt.Errorf("文件键不能为空")
	}
	if c.FileName == "" {
		return fmt.Errorf("文件名不能为空")
	}
	if c.Operation == "" {
		return fmt.Errorf("操作类型不能为空")
	}

	// 验证操作类型
	validOperations := map[string]bool{
		OperationUpload: true,
		OperationDelete: true,
		OperationCopy:   true,
		OperationMove:   true,
	}
	if !validOperations[c.Operation] {
		return fmt.Errorf("无效的操作类型: %s", c.Operation)
	}

	// 验证状态
	if c.Status != "" {
		validStatuses := map[string]bool{
			StatusSuccess: true,
			StatusFailed:  true,
			StatusPending: true,
		}
		if !validStatuses[c.Status] {
			return fmt.Errorf("无效的状态: %s", c.Status)
		}
	}

	return nil
}

// SetDefaults 设置默认值
func (c *CreateFileRecord) SetDefaults() {
	if c.Status == "" {
		c.Status = StatusSuccess
	}
}

// ToFileRecord 转换为文件记录模型
func (c *CreateFileRecord) ToFileRecord() *FileRecord {
	c.SetDefaults()

	return &FileRecord{
		FileKey:     c.FileKey,
		FileName:    c.FileName,
		FileSize:    c.FileSize,
		ContentType: c.ContentType,
		ETag:        c.ETag,
		FileURL:     c.FileURL,
		UploadPath:  c.UploadPath,
		Operation:   c.Operation,
		Status:      c.Status,
		ErrorMsg:    c.ErrorMsg,
		ClientIP:    c.ClientIP,
		UserAgent:   c.UserAgent,
	}
}

// SetQueryDefaults 设置查询默认值
func (q *FileRecordQuery) SetQueryDefaults() {
	if q.Page <= 0 {
		q.Page = 1
	}
	if q.PageSize <= 0 {
		q.PageSize = 20
	}
	if q.PageSize > 100 {
		q.PageSize = 100
	}
	if q.OrderBy == "" {
		q.OrderBy = "created_at"
	}
	if q.OrderDir == "" {
		q.OrderDir = "desc"
	}
}
