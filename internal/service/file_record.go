package service

import (
	"fmt"
	"strings"
	"time"

	"filesystem/internal/model"
	"filesystem/pkg/database"
	"filesystem/pkg/logger"

	"gorm.io/gorm"
)

// FileRecordService 文件记录服务
type FileRecordService struct {
	db *gorm.DB
}

// NewFileRecordService 创建文件记录服务
func NewFileRecordService() *FileRecordService {
	return &FileRecordService{
		db: database.GetDB(),
	}
}

// Create 创建文件记录
func (s *FileRecordService) Create(record *model.CreateFileRecord) (*model.FileRecord, error) {
	if err := record.Validate(); err != nil {
		return nil, fmt.Errorf("验证文件记录参数失败: %v", err)
	}

	fileRecord := record.ToFileRecord()
	
	if err := s.db.Create(fileRecord).Error; err != nil {
		logger.WithError(err).Errorf("创建文件记录失败: %s", record.FileKey)
		return nil, fmt.Errorf("创建文件记录失败: %v", err)
	}

	logger.Infof("文件记录创建成功: ID=%d, FileKey=%s", fileRecord.ID, fileRecord.FileKey)
	return fileRecord, nil
}

// GetByID 根据ID获取文件记录
func (s *FileRecordService) GetByID(id uint) (*model.FileRecord, error) {
	var record model.FileRecord
	if err := s.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("文件记录不存在: ID=%d", id)
		}
		logger.WithError(err).Errorf("获取文件记录失败: ID=%d", id)
		return nil, fmt.Errorf("获取文件记录失败: %v", err)
	}

	return &record, nil
}

// GetByFileKey 根据文件键获取文件记录
func (s *FileRecordService) GetByFileKey(fileKey string) (*model.FileRecord, error) {
	var record model.FileRecord
	if err := s.db.Where("file_key = ?", fileKey).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("文件记录不存在: FileKey=%s", fileKey)
		}
		logger.WithError(err).Errorf("获取文件记录失败: FileKey=%s", fileKey)
		return nil, fmt.Errorf("获取文件记录失败: %v", err)
	}

	return &record, nil
}

// Update 更新文件记录
func (s *FileRecordService) Update(id uint, updates *model.UpdateFileRecord) (*model.FileRecord, error) {
	var record model.FileRecord
	if err := s.db.First(&record, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("文件记录不存在: ID=%d", id)
		}
		return nil, fmt.Errorf("获取文件记录失败: %v", err)
	}

	if err := s.db.Model(&record).Updates(updates).Error; err != nil {
		logger.WithError(err).Errorf("更新文件记录失败: ID=%d", id)
		return nil, fmt.Errorf("更新文件记录失败: %v", err)
	}

	logger.Infof("文件记录更新成功: ID=%d", id)
	return &record, nil
}

// Delete 删除文件记录（软删除）
func (s *FileRecordService) Delete(id uint) error {
	if err := s.db.Delete(&model.FileRecord{}, id).Error; err != nil {
		logger.WithError(err).Errorf("删除文件记录失败: ID=%d", id)
		return fmt.Errorf("删除文件记录失败: %v", err)
	}

	logger.Infof("文件记录删除成功: ID=%d", id)
	return nil
}

// List 获取文件记录列表
func (s *FileRecordService) List(query *model.FileRecordQuery) (*model.FileRecordList, error) {
	query.SetQueryDefaults()

	db := s.db.Model(&model.FileRecord{})

	// 构建查询条件
	if query.FileKey != "" {
		db = db.Where("file_key = ?", query.FileKey)
	}
	if query.FileName != "" {
		db = db.Where("file_name LIKE ?", "%"+query.FileName+"%")
	}
	if query.Operation != "" {
		db = db.Where("operation = ?", query.Operation)
	}
	if query.Status != "" {
		db = db.Where("status = ?", query.Status)
	}
	if query.ClientIP != "" {
		db = db.Where("client_ip = ?", query.ClientIP)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		logger.WithError(err).Error("获取文件记录总数失败")
		return nil, fmt.Errorf("获取文件记录总数失败: %v", err)
	}

	// 排序
	orderBy := query.OrderBy
	if query.OrderDir == "desc" {
		orderBy += " DESC"
	} else {
		orderBy += " ASC"
	}
	db = db.Order(orderBy)

	// 分页
	offset := (query.Page - 1) * query.PageSize
	db = db.Offset(offset).Limit(query.PageSize)

	// 获取记录
	var records []model.FileRecord
	if err := db.Find(&records).Error; err != nil {
		logger.WithError(err).Error("获取文件记录列表失败")
		return nil, fmt.Errorf("获取文件记录列表失败: %v", err)
	}

	logger.Infof("获取文件记录列表成功: 总数=%d, 页码=%d, 每页=%d", total, query.Page, query.PageSize)

	return &model.FileRecordList{
		Records:  records,
		Total:    total,
		Page:     query.Page,
		PageSize: query.PageSize,
	}, nil
}

// GetStats 获取文件记录统计信息
func (s *FileRecordService) GetStats() (*model.FileRecordStats, error) {
	var stats model.FileRecordStats

	// 总文件数和总大小
	if err := s.db.Model(&model.FileRecord{}).
		Select("COUNT(*) as total_files, COALESCE(SUM(file_size), 0) as total_size").
		Where("operation = ? AND status = ?", model.OperationUpload, model.StatusSuccess).
		Scan(&stats).Error; err != nil {
		return nil, fmt.Errorf("获取文件统计失败: %v", err)
	}

	// 各种操作次数
	operations := []struct {
		operation string
		count     *int64
	}{
		{model.OperationUpload, &stats.UploadCount},
		{model.OperationDelete, &stats.DeleteCount},
		{model.OperationCopy, &stats.CopyCount},
	}

	for _, op := range operations {
		if err := s.db.Model(&model.FileRecord{}).
			Where("operation = ?", op.operation).
			Count(op.count).Error; err != nil {
			return nil, fmt.Errorf("获取操作统计失败: %v", err)
		}
	}

	// 成功和失败次数
	if err := s.db.Model(&model.FileRecord{}).
		Where("status = ?", model.StatusSuccess).
		Count(&stats.SuccessCount).Error; err != nil {
		return nil, fmt.Errorf("获取成功统计失败: %v", err)
	}

	if err := s.db.Model(&model.FileRecord{}).
		Where("status = ?", model.StatusFailed).
		Count(&stats.FailedCount).Error; err != nil {
		return nil, fmt.Errorf("获取失败统计失败: %v", err)
	}

	// 时间范围统计
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	timeRanges := []struct {
		start time.Time
		count *int64
	}{
		{today, &stats.TodayUploads},
		{weekStart, &stats.WeekUploads},
		{monthStart, &stats.MonthUploads},
	}

	for _, tr := range timeRanges {
		if err := s.db.Model(&model.FileRecord{}).
			Where("operation = ? AND status = ? AND created_at >= ?", 
				model.OperationUpload, model.StatusSuccess, tr.start).
			Count(tr.count).Error; err != nil {
			return nil, fmt.Errorf("获取时间范围统计失败: %v", err)
		}
	}

	logger.Info("获取文件记录统计信息成功")
	return &stats, nil
}

// BatchDelete 批量删除文件记录
func (s *FileRecordService) BatchDelete(ids []uint) error {
	if len(ids) == 0 {
		return fmt.Errorf("删除ID列表不能为空")
	}

	if err := s.db.Delete(&model.FileRecord{}, ids).Error; err != nil {
		logger.WithError(err).Errorf("批量删除文件记录失败: IDs=%v", ids)
		return fmt.Errorf("批量删除文件记录失败: %v", err)
	}

	logger.Infof("批量删除文件记录成功: 数量=%d", len(ids))
	return nil
}

// Search 搜索文件记录
func (s *FileRecordService) Search(keyword string, limit int) ([]model.FileRecord, error) {
	if limit <= 0 {
		limit = 10
	}
	if limit > 100 {
		limit = 100
	}

	var records []model.FileRecord
	keyword = "%" + strings.TrimSpace(keyword) + "%"

	if err := s.db.Where("file_name LIKE ? OR file_key LIKE ?", keyword, keyword).
		Order("created_at DESC").
		Limit(limit).
		Find(&records).Error; err != nil {
		logger.WithError(err).Errorf("搜索文件记录失败: keyword=%s", keyword)
		return nil, fmt.Errorf("搜索文件记录失败: %v", err)
	}

	logger.Infof("搜索文件记录成功: keyword=%s, 结果数=%d", keyword, len(records))
	return records, nil
}
