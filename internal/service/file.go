package service

import (
	"context"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"filesystem/internal/model"
	"filesystem/pkg/cos"
	"filesystem/pkg/logger"
)

// FileService 文件服务
type FileService struct {
	cosClient *cos.Client
}

// NewFileService 创建文件服务
func NewFileService(cosClient *cos.Client) *FileService {
	return &FileService{
		cosClient: cosClient,
	}
}

// Upload 上传文件
func (s *FileService) Upload(ctx context.Context, fileName string, reader io.Reader, req *model.UploadRequest) (*model.UploadResponse, error) {
	// 生成文件键
	fileKey := s.generateFileKey(fileName, req.Path)

	// 检查文件是否存在
	if !req.Overwrite {
		exists, err := s.cosClient.Exists(ctx, fileKey)
		if err != nil {
			logger.WithError(err).Errorf("检查文件是否存在失败: %s", fileKey)
			return nil, fmt.Errorf("检查文件是否存在失败: %v", err)
		}
		if exists {
			return nil, fmt.Errorf("文件已存在: %s", fileKey)
		}
	}

	// 上传文件
	result, err := s.cosClient.Upload(ctx, fileKey, reader, req.ContentType)
	if err != nil {
		logger.WithError(err).Errorf("上传文件失败: %s", fileKey)
		return nil, err
	}

	logger.Infof("文件上传成功: %s, 大小: %d", fileKey, result.Size)

	return &model.UploadResponse{
		FileName:    fileName,
		FileKey:     result.Key,
		FileSize:    result.Size,
		FileURL:     result.URL,
		ContentType: result.ContentType,
		ETag:        result.ETag,
	}, nil
}

// Download 下载文件
func (s *FileService) Download(ctx context.Context, key string) (io.ReadCloser, *model.FileInfoResponse, error) {
	// 检查文件是否存在
	exists, err := s.cosClient.Exists(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("检查文件是否存在失败: %s", key)
		return nil, nil, fmt.Errorf("检查文件是否存在失败: %v", err)
	}
	if !exists {
		return nil, nil, fmt.Errorf("文件不存在: %s", key)
	}

	// 获取文件信息
	fileInfo, err := s.cosClient.GetFileInfo(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("获取文件信息失败: %s", key)
		return nil, nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	// 下载文件
	reader, err := s.cosClient.Download(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("下载文件失败: %s", key)
		return nil, nil, err
	}

	logger.Infof("文件下载成功: %s", key)

	info := &model.FileInfoResponse{
		Key:          fileInfo.Key,
		Name:         filepath.Base(fileInfo.Key),
		Size:         fileInfo.Size,
		LastModified: fileInfo.LastModified,
		ETag:         fileInfo.ETag,
		ContentType:  fileInfo.ContentType,
		URL:          fileInfo.URL,
		Exists:       true,
	}

	return reader, info, nil
}

// Delete 删除文件
func (s *FileService) Delete(ctx context.Context, key string) error {
	// 检查文件是否存在
	exists, err := s.cosClient.Exists(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("检查文件是否存在失败: %s", key)
		return fmt.Errorf("检查文件是否存在失败: %v", err)
	}
	if !exists {
		return fmt.Errorf("文件不存在: %s", key)
	}

	// 删除文件
	if err := s.cosClient.Delete(ctx, key); err != nil {
		logger.WithError(err).Errorf("删除文件失败: %s", key)
		return err
	}

	logger.Infof("文件删除成功: %s", key)
	return nil
}

// List 列出文件
func (s *FileService) List(ctx context.Context, req *model.ListRequest) (*model.ListResponse, error) {
	result, err := s.cosClient.List(ctx, req.Prefix, req.Delimiter, req.Marker, req.MaxKeys)
	if err != nil {
		logger.WithError(err).Errorf("列出文件失败: prefix=%s", req.Prefix)
		return nil, err
	}

	// 转换文件信息
	files := make([]model.FileItem, 0, len(result.Files))
	for _, file := range result.Files {
		files = append(files, model.FileItem{
			Key:          file.Key,
			Name:         filepath.Base(file.Key),
			Size:         file.Size,
			LastModified: file.LastModified,
			ETag:         file.ETag,
			ContentType:  file.ContentType,
			URL:          file.URL,
		})
	}

	logger.Infof("列出文件成功: prefix=%s, 文件数=%d, 目录数=%d", req.Prefix, len(files), len(result.Directories))

	return &model.ListResponse{
		Files:       files,
		Directories: result.Directories,
		Prefix:      result.Prefix,
		Marker:      result.Marker,
		IsTruncated: result.IsTruncated,
		NextMarker:  result.NextMarker,
	}, nil
}

// GetFileInfo 获取文件信息
func (s *FileService) GetFileInfo(ctx context.Context, key string) (*model.FileInfoResponse, error) {
	// 检查文件是否存在
	exists, err := s.cosClient.Exists(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("检查文件是否存在失败: %s", key)
		return nil, fmt.Errorf("检查文件是否存在失败: %v", err)
	}

	if !exists {
		return &model.FileInfoResponse{
			Key:    key,
			Name:   filepath.Base(key),
			Exists: false,
		}, nil
	}

	// 获取文件信息
	fileInfo, err := s.cosClient.GetFileInfo(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("获取文件信息失败: %s", key)
		return nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	logger.Infof("获取文件信息成功: %s", key)

	return &model.FileInfoResponse{
		Key:          fileInfo.Key,
		Name:         filepath.Base(fileInfo.Key),
		Size:         fileInfo.Size,
		LastModified: fileInfo.LastModified,
		ETag:         fileInfo.ETag,
		ContentType:  fileInfo.ContentType,
		URL:          fileInfo.URL,
		Exists:       true,
	}, nil
}

// Copy 复制文件
func (s *FileService) Copy(ctx context.Context, req *model.CopyRequest) (*model.CopyResponse, error) {
	// 检查源文件是否存在
	exists, err := s.cosClient.Exists(ctx, req.SourceKey)
	if err != nil {
		logger.WithError(err).Errorf("检查源文件是否存在失败: %s", req.SourceKey)
		return nil, fmt.Errorf("检查源文件是否存在失败: %v", err)
	}
	if !exists {
		return nil, fmt.Errorf("源文件不存在: %s", req.SourceKey)
	}

	// 复制文件
	if err := s.cosClient.Copy(ctx, req.SourceKey, req.DestKey); err != nil {
		logger.WithError(err).Errorf("复制文件失败: %s -> %s", req.SourceKey, req.DestKey)
		return nil, err
	}

	logger.Infof("文件复制成功: %s -> %s", req.SourceKey, req.DestKey)

	return &model.CopyResponse{
		SourceKey: req.SourceKey,
		DestKey:   req.DestKey,
		Success:   true,
	}, nil
}

// BatchDelete 批量删除文件
func (s *FileService) BatchDelete(ctx context.Context, req *model.BatchDeleteRequest) (*model.BatchDeleteResponse, error) {
	success := make([]string, 0)
	failed := make([]string, 0)

	for _, key := range req.Keys {
		if err := s.Delete(ctx, key); err != nil {
			logger.WithError(err).Errorf("批量删除文件失败: %s", key)
			failed = append(failed, key)
		} else {
			success = append(success, key)
		}
	}

	logger.Infof("批量删除文件完成: 成功=%d, 失败=%d", len(success), len(failed))

	return &model.BatchDeleteResponse{
		Success: success,
		Failed:  failed,
	}, nil
}

// Exists 检查文件是否存在
func (s *FileService) Exists(ctx context.Context, key string) (*model.ExistsResponse, error) {
	exists, err := s.cosClient.Exists(ctx, key)
	if err != nil {
		logger.WithError(err).Errorf("检查文件是否存在失败: %s", key)
		return nil, fmt.Errorf("检查文件是否存在失败: %v", err)
	}

	logger.Infof("检查文件是否存在: %s, 结果: %v", key, exists)

	return &model.ExistsResponse{
		Key:    key,
		Exists: exists,
	}, nil
}

// generateFileKey 生成文件键
func (s *FileService) generateFileKey(fileName, path string) string {
	// 清理路径
	path = strings.Trim(path, "/")

	// 生成时间前缀（可选）
	timePrefix := time.Now().Format("2006/01/02")

	if path == "" {
		return fmt.Sprintf("%s/%s", timePrefix, fileName)
	}

	return fmt.Sprintf("%s/%s/%s", timePrefix, path, fileName)
}
