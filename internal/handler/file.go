package handler

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"

	"filesystem/internal/model"
	"filesystem/internal/service"
	"filesystem/pkg/logger"
	"filesystem/pkg/response"

	"github.com/gin-gonic/gin"
)

// FileHandler 文件处理器
type FileHandler struct {
	fileService *service.FileService
}

// NewFileHandler 创建文件处理器
func NewFileHandler(fileService *service.FileService) *FileHandler {
	return &FileHandler{
		fileService: fileService,
	}
}

// Upload 上传文件
func (h *FileHandler) Upload(c *gin.Context) {
	var req model.UploadRequest
	if err := c.ShouldBind(&req); err != nil {
		logger.WithError(err).Error("绑定上传请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		logger.WithError(err).Error("获取上传文件失败")
		response.BadRequest(c, "请选择要上传的文件")
		return
	}
	defer file.Close()

	// 如果没有指定内容类型，使用文件头中的类型
	if req.ContentType == "" {
		req.ContentType = header.Header.Get("Content-Type")
	}

	// 上传文件
	result, err := h.fileService.Upload(c.Request.Context(), header.Filename, file, &req)
	if err != nil {
		logger.WithError(err).Errorf("上传文件失败: %s", header.Filename)
		response.COSError(c, "上传文件失败", err)
		return
	}

	logger.Infof("文件上传成功: %s", result.FileKey)
	response.SuccessWithMessage(c, "文件上传成功", result)
}

// Download 下载文件
func (h *FileHandler) Download(c *gin.Context) {
	var req model.DownloadRequest
	if err := c.ShouldBindUri(&req); err != nil {
		logger.WithError(err).Error("绑定下载请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 下载文件
	reader, fileInfo, err := h.fileService.Download(c.Request.Context(), req.Key)
	if err != nil {
		logger.WithError(err).Errorf("下载文件失败: %s", req.Key)
		response.COSError(c, "下载文件失败", err)
		return
	}
	defer reader.Close()

	// 设置响应头
	c.Header("Content-Type", fileInfo.ContentType)
	c.Header("Content-Length", strconv.FormatInt(fileInfo.Size, 10))
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", fileInfo.Name))
	c.Header("ETag", fileInfo.ETag)

	// 流式传输文件内容
	c.DataFromReader(http.StatusOK, fileInfo.Size, fileInfo.ContentType, reader, nil)
	
	logger.Infof("文件下载成功: %s", req.Key)
}

// Delete 删除文件
func (h *FileHandler) Delete(c *gin.Context) {
	var req model.DeleteRequest
	if err := c.ShouldBindUri(&req); err != nil {
		logger.WithError(err).Error("绑定删除请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 删除文件
	if err := h.fileService.Delete(c.Request.Context(), req.Key); err != nil {
		logger.WithError(err).Errorf("删除文件失败: %s", req.Key)
		response.COSError(c, "删除文件失败", err)
		return
	}

	logger.Infof("文件删除成功: %s", req.Key)
	response.SuccessWithMessage(c, "文件删除成功", gin.H{"key": req.Key})
}

// List 列出文件
func (h *FileHandler) List(c *gin.Context) {
	var req model.ListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.WithError(err).Error("绑定列表请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 设置默认值
	if req.MaxKeys <= 0 {
		req.MaxKeys = 100
	}
	if req.MaxKeys > 1000 {
		req.MaxKeys = 1000
	}

	// 列出文件
	result, err := h.fileService.List(c.Request.Context(), &req)
	if err != nil {
		logger.WithError(err).Errorf("列出文件失败: prefix=%s", req.Prefix)
		response.COSError(c, "列出文件失败", err)
		return
	}

	logger.Infof("列出文件成功: prefix=%s, 文件数=%d", req.Prefix, len(result.Files))
	response.Success(c, result)
}

// GetFileInfo 获取文件信息
func (h *FileHandler) GetFileInfo(c *gin.Context) {
	var req model.FileInfoRequest
	if err := c.ShouldBindUri(&req); err != nil {
		logger.WithError(err).Error("绑定文件信息请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 获取文件信息
	result, err := h.fileService.GetFileInfo(c.Request.Context(), req.Key)
	if err != nil {
		logger.WithError(err).Errorf("获取文件信息失败: %s", req.Key)
		response.COSError(c, "获取文件信息失败", err)
		return
	}

	logger.Infof("获取文件信息成功: %s", req.Key)
	response.Success(c, result)
}

// Copy 复制文件
func (h *FileHandler) Copy(c *gin.Context) {
	var req model.CopyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WithError(err).Error("绑定复制请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 复制文件
	result, err := h.fileService.Copy(c.Request.Context(), &req)
	if err != nil {
		logger.WithError(err).Errorf("复制文件失败: %s -> %s", req.SourceKey, req.DestKey)
		response.COSError(c, "复制文件失败", err)
		return
	}

	logger.Infof("文件复制成功: %s -> %s", req.SourceKey, req.DestKey)
	response.SuccessWithMessage(c, "文件复制成功", result)
}

// BatchDelete 批量删除文件
func (h *FileHandler) BatchDelete(c *gin.Context) {
	var req model.BatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.WithError(err).Error("绑定批量删除请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 批量删除文件
	result, err := h.fileService.BatchDelete(c.Request.Context(), &req)
	if err != nil {
		logger.WithError(err).Error("批量删除文件失败")
		response.COSError(c, "批量删除文件失败", err)
		return
	}

	logger.Infof("批量删除文件完成: 成功=%d, 失败=%d", len(result.Success), len(result.Failed))
	response.SuccessWithMessage(c, "批量删除文件完成", result)
}

// Exists 检查文件是否存在
func (h *FileHandler) Exists(c *gin.Context) {
	var req model.ExistsRequest
	if err := c.ShouldBindUri(&req); err != nil {
		logger.WithError(err).Error("绑定检查存在请求参数失败")
		response.BadRequestWithDetail(c, "请求参数错误", err)
		return
	}

	// 检查文件是否存在
	result, err := h.fileService.Exists(c.Request.Context(), req.Key)
	if err != nil {
		logger.WithError(err).Errorf("检查文件是否存在失败: %s", req.Key)
		response.COSError(c, "检查文件是否存在失败", err)
		return
	}

	logger.Infof("检查文件是否存在: %s, 结果: %v", req.Key, result.Exists)
	response.Success(c, result)
}

// Health 健康检查
func (h *FileHandler) Health(c *gin.Context) {
	response.Success(c, gin.H{
		"status":    "ok",
		"timestamp": gin.H{},
		"service":   "filesystem",
	})
}
