package handler

import (
	"filesystem/internal/service"
	"filesystem/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Router 路由器
type Router struct {
	fileHandler *FileHandler
}

// NewRouter 创建路由器
func NewRouter(fileService *service.FileService) *Router {
	return &Router{
		fileHandler: NewFileHandler(fileService),
	}
}

// SetupRoutes 设置路由
func (r *Router) SetupRoutes(engine *gin.Engine) {
	// 中间件
	engine.Use(gin.Logger())
	engine.Use(gin.Recovery())
	engine.Use(corsMiddleware())
	engine.Use(loggerMiddleware())

	// API 路由组
	api := engine.Group("/api/v1")
	{
		// 健康检查
		api.GET("/health", r.fileHandler.Health)

		// 文件操作
		files := api.Group("/files")
		{
			// 上传文件
			files.POST("/upload", r.fileHandler.Upload)
			
			// 下载文件
			files.GET("/download/*key", r.fileHandler.Download)
			
			// 删除文件
			files.DELETE("/*key", r.fileHandler.Delete)
			
			// 列出文件
			files.GET("/", r.fileHandler.List)
			
			// 获取文件信息
			files.GET("/info/*key", r.fileHandler.GetFileInfo)
			
			// 复制文件
			files.POST("/copy", r.fileHandler.Copy)
			
			// 批量删除文件
			files.POST("/batch-delete", r.fileHandler.BatchDelete)
			
			// 检查文件是否存在
			files.GET("/exists/*key", r.fileHandler.Exists)
		}
	}

	// 根路径重定向到健康检查
	engine.GET("/", func(c *gin.Context) {
		c.Redirect(301, "/api/v1/health")
	})
}

// corsMiddleware CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Content-Disposition")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// loggerMiddleware 日志中间件
func loggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.WithFields(logger.GetLogger().WithFields(map[string]interface{}{
			"status_code":  param.StatusCode,
			"latency":      param.Latency,
			"client_ip":    param.ClientIP,
			"method":       param.Method,
			"path":         param.Path,
			"error":        param.ErrorMessage,
			"body_size":    param.BodySize,
			"user_agent":   param.Request.UserAgent(),
		}).Data).Info("HTTP Request")
		return ""
	})
}
