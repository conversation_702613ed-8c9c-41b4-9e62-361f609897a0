package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"filesystem/config"
	"filesystem/internal/handler"
	"filesystem/internal/service"
	"filesystem/pkg/cos"
	"filesystem/pkg/logger"

	"github.com/gin-gonic/gin"
)

var (
	configPath = flag.String("config", "", "配置文件路径")
	help       = flag.Bool("help", false, "显示帮助信息")
	version    = flag.Bool("version", false, "显示版本信息")
)

const (
	AppName    = "filesystem"
	AppVersion = "1.0.0"
)

func main() {
	flag.Parse()

	// 显示帮助信息
	if *help {
		showHelp()
		return
	}

	// 显示版本信息
	if *version {
		showVersion()
		return
	}

	// 加载配置
	cfg, err := loadConfig(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	if err := initLogger(cfg); err != nil {
		log.Fatalf("初始化日志失败: %v", err)
	}

	logger.Infof("启动 %s v%s", AppName, AppVersion)

	// 初始化 COS 客户端
	cosClient, err := initCOSClient(cfg)
	if err != nil {
		logger.Fatalf("初始化 COS 客户端失败: %v", err)
	}

	// 初始化服务
	fileService := service.NewFileService(cosClient)

	// 初始化路由
	router := handler.NewRouter(fileService)

	// 设置 Gin 模式
	gin.SetMode(cfg.Server.Mode)

	// 创建 Gin 引擎
	engine := gin.New()

	// 设置路由
	router.SetupRoutes(engine)

	// 启动服务器
	addr := ":" + cfg.Server.Port
	logger.Infof("服务器启动在端口 %s", cfg.Server.Port)
	logger.Infof("健康检查: http://localhost%s/api/v1/health", addr)
	logger.Infof("API 文档: http://localhost%s/api/v1/files", addr)

	if err := engine.Run(addr); err != nil {
		logger.Fatalf("启动服务器失败: %v", err)
	}
}

// loadConfig 加载配置
func loadConfig(configPath string) (*config.Config, error) {
	if configPath == "" {
		configPath = config.GetConfigPath()
	}

	// 如果配置文件不存在，创建示例配置文件
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		logger.Warnf("配置文件不存在: %s", configPath)

		// 创建示例配置文件
		examplePath := "config/config.yaml"
		if err := config.CreateExampleConfig(examplePath); err != nil {
			return nil, fmt.Errorf("创建示例配置文件失败: %v", err)
		}

		logger.Infof("已创建示例配置文件: %s", examplePath)
		logger.Info("请复制并修改配置文件:")
		logger.Infof("  cp %s config/config.yaml", examplePath)
		logger.Info("然后修改 config/config.yaml 中的配置参数")

		os.Exit(1)
	}

	return config.Load(configPath)
}

// initLogger 初始化日志
func initLogger(cfg *config.Config) error {
	logConfig := logger.Config{
		Level:  cfg.Log.Level,
		Format: cfg.Log.Format,
		Output: cfg.Log.Output,
		File:   cfg.Log.File,
	}
	return logger.Init(logConfig)
}

// initCOSClient 初始化 COS 客户端
func initCOSClient(cfg *config.Config) (*cos.Client, error) {
	cosConfig := cos.Config{
		SecretID:  cfg.COS.SecretID,
		SecretKey: cfg.COS.SecretKey,
		Region:    cfg.COS.Region,
		Bucket:    cfg.COS.Bucket,
		BaseURL:   cfg.COS.BaseURL,
	}

	// 验证必要的配置
	if cosConfig.SecretID == "" || cosConfig.SecretID == "your-secret-id" {
		return nil, fmt.Errorf("请配置有效的 COS SecretID")
	}
	if cosConfig.SecretKey == "" || cosConfig.SecretKey == "your-secret-key" {
		return nil, fmt.Errorf("请配置有效的 COS SecretKey")
	}
	if cosConfig.Bucket == "" || cosConfig.Bucket == "your-bucket-name" {
		return nil, fmt.Errorf("请配置有效的 COS Bucket")
	}
	if cosConfig.BaseURL == "" || cosConfig.BaseURL == "https://your-bucket-name.cos.ap-beijing.myqcloud.com" {
		return nil, fmt.Errorf("请配置有效的 COS BaseURL")
	}

	return cos.NewClient(cosConfig)
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Printf("%s v%s - 腾讯云 COS 对象存储服务\n\n", AppName, AppVersion)
	fmt.Println("使用方法:")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])
	fmt.Println("选项:")
	fmt.Println("  -config string    配置文件路径 (默认: config/config.yaml)")
	fmt.Println("  -help             显示帮助信息")
	fmt.Println("  -version          显示版本信息")
	fmt.Println()
	fmt.Println("环境变量:")
	fmt.Println("  CONFIG_PATH       配置文件路径")
	fmt.Println("  SERVER_PORT       服务端口")
	fmt.Println("  SERVER_MODE       服务模式 (debug/release/test)")
	fmt.Println("  COS_SECRET_ID     腾讯云 SecretID")
	fmt.Println("  COS_SECRET_KEY    腾讯云 SecretKey")
	fmt.Println("  COS_REGION        COS 地域")
	fmt.Println("  COS_BUCKET        存储桶名称")
	fmt.Println("  COS_BASE_URL      存储桶访问域名")
	fmt.Println("  LOG_LEVEL         日志级别 (debug/info/warn/error)")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Printf("  %s -config config/config.yaml\n", os.Args[0])
	fmt.Printf("  CONFIG_PATH=config/prod.yaml %s\n", os.Args[0])
}

// showVersion 显示版本信息
func showVersion() {
	fmt.Printf("%s v%s\n", AppName, AppVersion)
}
